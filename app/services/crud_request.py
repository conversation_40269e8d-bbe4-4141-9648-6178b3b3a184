from sqlalchemy.future import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, insert, update, delete, func, any_
from datetime import datetime
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from sqlalchemy.orm import aliased
import json

from models import models
from services import crud_noti as crud_noti, crud as crud
from core.errors import DatabaseError, NotFoundError, DuplicateError, ValidationError

async def create_request(session: AsyncSession, request: dict):
    try:
        if request.get("request_type") == "access-platform" and request.get("platform_name") == "funnel-tool":
            request["status"] = "approved"

        stmt = insert(models.requests).values(request).returning(*models.requests.c)
        request = (await session.execute(stmt)).mappings().first()
        print("1. Request created successfully")

        # Thêm log liên quan đến ticket
        log_data = {
            "request_id": request.get("id"),
            "log_type": "init_request",
            "value": request.get("title"),
            "changed_by": request.get("requester_id"),
            "note": "Init Request",
        }
        stmt_log = insert(models.request_logs).values(log_data)
        await session.execute(stmt_log)
        print("2. Request log created successfully")

        if request.get("request_type") == "access-platform" and request.get("platform_name") == "funnel-tool":
            # Thêm log liên quan đến ticket
            log_data = {
                "request_id": request.get("id"),
                "log_type": "change_status",
                "value": "approved",
                "changed_by": 2, #admin
                "note": "Auto approved by system",
            }
            stmt_log = insert(models.request_logs).values(log_data)
            await session.execute(stmt_log)
            print("3. Request log created successfully (auto approved for funnel-tool platform)")

        # Send notification to reviewer, approver, executor
        reviewer_id = request.get("reviewer_id")
        approver_id = request.get("approver_id")
        executor_id = request.get("executor_id")
        executors = request.get("executors")
        title = "New Request"
        content = f"Request #{request.get('id')} has been created. Please check and process it."
        type = "request"

        # print("Reviewer id:", reviewer_id)
        # print("Approver id:", approver_id)
        # print("Executor id:", executor_id)
        # print("Executors:", executors)
        if reviewer_id:
            await crud_noti.create_notification(session, reviewer_id, title, content, type)
        elif approver_id:
            await crud_noti.create_notification(session, approver_id, title, content, type)
        elif executor_id:
            await crud_noti.create_notification(session, executor_id, title, content, type)
        elif executors:
            for executor in executors:
                executor_id = (await session.execute(select(models.users.c.id).where(models.users.c.username == executor))).scalar()
                await crud_noti.create_notification(session, executor_id, title, content, type)

        await session.commit()
        return request
    except IntegrityError as e:
        if "duplicate key" in str(e).lower():
            raise DuplicateError(
                f"Request with title '{request.get('title')}' already exists")
        raise DatabaseError(f"Database integrity error: {str(e)}")
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error creating request: {str(e)}")

async def get_request_by_id(session: AsyncSession, request_id: int, user_role: str, user_id: int, username: str):
    try:
        requester = aliased(models.users, name="requester")
        reviewer = aliased(models.users, name="reviewer")
        approver = aliased(models.users, name="approver")
        executor = aliased(models.users, name="executor")
        stmt = (
            select(
                models.requests,
                requester.c.display_name.label("requester_name"),
                reviewer.c.display_name.label("reviewer_name"),
                approver.c.display_name.label("approver_name"),
                executor.c.display_name.label("executor_name"),
            )
            .select_from(models.requests)
            .join(requester, models.requests.c.requester_id == requester.c.id, isouter=True)
            .join(reviewer, models.requests.c.reviewer_id == reviewer.c.id, isouter=True)
            .join(approver, models.requests.c.approver_id == approver.c.id, isouter=True)
            .join(executor, models.requests.c.executor_id == executor.c.id, isouter=True)
            .where(models.requests.c.id == request_id)
        )
        request_base = (await session.execute(stmt)).mappings().first()

        # print("Requester id:", request_base.get("requester_id"))
        # print("Reviewer id:", request_base.get("reviewer_id"))
        # print("Approver id:", request_base.get("approver_id"))
        # print("Executor id:", request_base.get("executor_id"))
        # print("Executors:", request_base.get("executors"))
        # print("User role:", user_role)
        # print("User id:", user_id)
        # print("Username:", username)
        if user_role == "admin":
            pass
        elif request_base.get("requester_id") == user_id:
            pass
        elif request_base.get("reviewer_id") == user_id:
            pass
        elif request_base.get("approver_id") == user_id:
            pass
        elif request_base.get("executor_id") and request_base.get("executor_id") == user_id:
            pass
        elif request_base.get("executors") and username in request_base.get("executors"):
            pass
        else:
            raise NotFoundError(f"You don't have permission to view this request")

        stmt_log = (
            select(
                models.request_logs,
                models.users.c.display_name.label("changed_by_name"),
            )
            .join(models.users, models.request_logs.c.changed_by == models.users.c.id, isouter=False)
            .where(models.request_logs.c.request_id == request_id)
            # .order_by(models.request_logs.c.created_at.desc())
            )
        request_logs = (await session.execute(stmt_log)).mappings().all()


        # return result.mappings().first()
        return {
            "request_base": request_base,
            "request_logs": request_logs,
        }
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error retrieving request by id: {str(e)}")


async def get_request_by_user_id(session: AsyncSession, user_id: int, username: str):
    try:
        if user_id == -1:
            stmt = select(models.requests)
        else:
            # stmt = select(models.requests).where(models.requests.c.requester_id == user_id)
            stmt = select(models.requests).where(models.requests.c.requester_id == user_id or models.requests.c.reviewer_id == user_id or models.requests.c.approver_id == user_id or models.requests.c.executor_id == user_id or models.requests.c.executors.contains(username))
        result = await session.execute(stmt)
        return result.mappings().all()
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error retrieving requests: {str(e)}")

async def update_request(session: AsyncSession, update_type: str, user_id: int, request_id: int, request: dict):
    try:
        stmt = update(models.requests).where(models.requests.c.id == request_id).values(request)
        await session.execute(stmt)
        # get request data after update
        request = (await session.execute(select(models.requests).where(models.requests.c.id == request_id))).mappings().first()
        # print("Request data after update:", request)
        print("1. Request updated successfully")

        if update_type == 'change_status':
            # Thêm log liên quan đến ticket
            log_data = {
                "request_id": request_id,
                "log_type": "change_status",
                "value": request.get("status"),
                "changed_by": user_id,
                "note": request.get("note") if request.get("note") else "",
            }
            stmt_log = insert(models.request_logs).values(log_data)
            await session.execute(stmt_log)
            print("2. Request log created successfully")

            # send notification to user
            title = "Request Status Changed"
            content = f"Request #{request_id} has been {request.get('status')}. Please check and process it."
            type = "request"

            if request.get("status") == "reviewed" and request.get("approver_id"):
                await crud_noti.create_notification(session, request.get("approver_id"), title, content, type)
                print(f"3. Notification sent to approver: {request.get('approver_id')}")
            elif request.get("status") == "approved":
                if request.get("executor_id"):
                    print("Executor id:", request.get("executor_id"))
                    await crud_noti.create_notification(session, request.get("executor_id"), title, content, type)
                    print(f"3.1 Notification sent to approver: {request.get('executor_id')}")
                elif request.get("executors") and len(request.get("executors")) > 0:
                    for executor in list(request.get("executors")):
                        executor_id = (await session.execute(select(models.users.c.id).where(models.users.c.username == executor))).scalar()
                        await crud_noti.create_notification(session, executor_id, title, content, type)
                        print(f"3.2 Notification sent to approver: {executor_id}")

            elif request.get("status") == "done":
                title = "Request Completed"
                content = f"Request #{request_id} has been completed."
                type = "request"
                await crud_noti.create_notification(session, request.get("requester_id"), title, content, type)

            await session.commit()
            return {"message": "Request updated successfully"}
    except SQLAlchemyError as e:
        await session.rollback()
        raise DatabaseError(f"Error updating request: {str(e)}")

async def update_report_needed_completion(report_needed_str: str, report_needed_updated: dict) -> str:
    """
    Update the completion status of views and workbooks in report_needed JSON

    Args:
        report_needed_str: JSON string of the current report_needed data
        report_needed_updated: Dict containing 'views' and 'workbooks' lists with IDs to mark as completed

    Returns:
        Updated JSON string with completion status updated and total_completed recalculated
    """
    try:
        # Parse the current report_needed JSON
        report_needed = json.loads(report_needed_str) if report_needed_str else {}

        # Get the lists of IDs to mark as completed
        completed_view_ids = [int(id_str) for id_str in report_needed_updated.get('views', [])]
        completed_workbook_ids = [int(id_str) for id_str in report_needed_updated.get('workbooks', [])]

        # Update completion status for views and workbooks in items_by_owner
        total_completed = 0

        for owner_data in report_needed.get('items_by_owner', {}).values():
            # Update views completion status
            for view in owner_data.get('views', []):
                if view['id'] in completed_view_ids:
                    view['completed'] = True
                if view.get('completed', False):
                    total_completed += 1

            # Update workbooks completion status
            for workbook in owner_data.get('workbooks', []):
                if workbook['id'] in completed_workbook_ids:
                    workbook['completed'] = True
                if workbook.get('completed', False):
                    total_completed += 1

        # Update the summary total_completed count
        if 'summary' in report_needed:
            report_needed['summary']['total_completed'] = total_completed

        return json.dumps(report_needed)

    except (json.JSONDecodeError, KeyError, ValueError) as e:
        raise ValidationError(f"Error processing report_needed update: {str(e)}")


async def process_request(session: AsyncSession, user_id: int, request_id: int, request_update: dict):
    try:
        request_data = (await session.execute(select(models.requests).where(models.requests.c.id == request_id))).mappings().first()
        if request_data.get("request_type") == "access-platform":
            user_username = (await session.execute(select(models.users).where(models.users.c.id == request_data.get("requester_id")))).mappings().first().get("username")

            request_platform = request_data.get("platform_name")
            if request_platform in ("funnel-tool", "whitelist-tool", "merchant-campaign"):
                platform = 'wlp'
            elif request_platform == "ads-report":
                platform = 'ads'
            elif request_platform == "sbv-report":
                platform = 'sbv'
            elif request_platform == "ecommerce-report":
                platform = 'ecommerce'
            else:
                raise ValidationError(f"Invalid platform: {request_platform}")

            user = await crud.get_user_by_platform(session, platform, user_username)

            # if user not exists, call create_zds_platform_user with platform = wlp to create user
            # if user exists, call update_zds_platform_user with platform = wlp to update user
            if not user:
                print(f"User not exists, creating user in {request_platform} with username {user_username}...")
                user = {
                    "username": user_username,
                }
                if request_platform == "funnel-tool":
                    user["role"] = ["funnel"]
                    user["role_lvl1"] = ["funnel_report", "funnel_event"]
                elif request_platform == "whitelist-tool":
                    user["role"] = ["whitelist"]
                elif request_platform == "merchant-campaign":
                    user["role"] = ["config_tool"]
                    user["role_lvl1"] = ["config_tool_view"] #config_tool_edit, config_tool_approve
                elif request_platform == "ads-report":
                    user["role"] = []
                elif request_data.get("platform_name") in ("sbv-report", "ecommerce-report"):
                    user["role"] = "all"
                else:
                    raise ValidationError(f"Invalid platform: {request_platform}")
                await crud.create_zds_platform_user(session, platform, **user)
            else:
                print(f"User exists, updating user in {request_platform} with username {user_username}...")
                if request_platform == "funnel-tool":
                    user_update = {
                        "role": list(set(user.get("role") + ["funnel"])),
                        "role_lvl1": list(set(user.get("role_lvl1") + ["funnel_report", "funnel_event"]))
                    }
                elif request_platform == "whitelist-tool":
                    user_update = {
                        "role": list(set(user.get("role") + ["whitelist"]))
                    }
                elif request_platform == "merchant-campaign":
                    user_update = {
                        "role": list(set(user.get("role") + ["config_tool"])),
                        "role_lvl1": list(set(user.get("role_lvl1") + ["config_tool_view"])) #config_tool_edit, config_tool_approve
                    }
                elif request_platform == "ads-report":
                    user_update = {
                        "role": list(set(user.get("role") + ["ads"]))
                    }
                elif request_data.get("platform_name") in ("sbv-report", "ecommerce-report"):
                    user_update = {
                        "role": "all"
                    }
                else:
                    raise ValidationError(f"Invalid platform: {request_platform}")
                await crud.update_zds_platform_user(session, platform, user.get("username"), **user_update)
            await update_request(session, "change_status", user_id, request_id, {"status": "in_progress"})
            await update_request(session, "change_status", user_id, request_id, {"status": "done"})

        elif request_data.get("request_type") == "access-report":
            print("Processing access-report request...")

            report_needed = request_data.get("report_needed")
            # print("report_needed:", report_needed)
            # print("report_needed_updated:", request_update.get("report_needed"))

            # Process update report_needed by using request_update.get("report_needed")
            if request_update.get("report_needed"):
                updated_report_needed = await update_report_needed_completion(
                    report_needed,
                    request_update.get("report_needed")
                )

                # Update the request with the new report_needed data
                await update_request(session, "change_status", user_id, request_id, {
                    "report_needed": updated_report_needed,
                    "status": "in_progress"
                })
                print("Report needed updated successfully")

                # Check if all items are completed and mark request as done
                updated_data = json.loads(updated_report_needed)
                if updated_data.get("summary", {}).get("total_items") == updated_data.get("summary", {}).get("total_completed"):
                    await update_request(session, "change_status", user_id, request_id, {"status": "done"})
                    print("All items completed - Request marked as done")

            user_username = ""
        elif request_data.get("request_type") == "access-database":
            pass
        elif request_data.get("request_type") == "adhoc-data":
            pass
        else:
            pass

        await session.commit()
        return {"message": "Request processed successfully", "data": {"request_id": request_id, "username": user_username}}
    except SQLAlchemyError as e:
        raise DatabaseError(f"Error processing request: {str(e)}")