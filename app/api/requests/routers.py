import os

from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.ext.asyncio import AsyncSession

import json

from schemas import schemas_request
from services import service_user, service_session, crud_request as crud
from core.errors import DatabaseError
from core.logger import log_info, log_error, log_warning


import core.config as config
config.get_config()

BASE_URL = os.environ.get("BASE_URL")
FE_URL = os.environ.get("FE_URL")
# BE_URL = os.environ.get("BE_URL")

EXCLUDE_PATH = os.environ.get("EXCLUDE_PATH")

itemrouter = APIRouter()


@itemrouter.post(f"{BASE_URL}/requests/create", tags=["requests"])
async def add_request(
    body: schemas_request.RequestCreate = Body(...),
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    try:
        body.requester_id = current_user.get("id")
        request = await crud.create_request(session, body.dict())
        return request
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))

@itemrouter.get(f"{BASE_URL}/requests/get", tags=["requests"])
async def get_requests(
    request_id: int,
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    try:
        requests = await crud.get_request_by_id(session, request_id, current_user.get("role"), current_user.get("id"), current_user.get("username"))
        return requests
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))

@itemrouter.get(f"{BASE_URL}/requests/getbyuser", tags=["requests"])
async def get_requests_by_user_id(
    user_id: int,
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    try:
        if current_user.get("role") == "admin":
            requests = await crud.get_request_by_user_id(session, -1)
        else:
            requests = await crud.get_request_by_user_id(session, current_user.get("id"), current_user.get("username"))
        return requests
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))

@itemrouter.post(f"{BASE_URL}/requests/update", tags=["requests"])
async def update_request(
    request_id: int,
    # body: schemas_request.RequestUpdate = Body(...),
    body: dict = Body(...),
    session: AsyncSession = Depends(service_session.get_db_session),
    current_user: dict = Depends(service_user.get_current_active_user)
):
    try:
        # print(body.dict().get("status"))
        if body.get("status") == "in_progress":
            print("Processing request...")
            result = await crud.process_request(session, current_user.get("id"), request_id, body)
        else:
            result = await crud.update_request(session, "change_status", current_user.get("id"), request_id, body)
        return result
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=str(e))