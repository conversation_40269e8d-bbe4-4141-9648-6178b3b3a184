"""
SQLAlchemy uses the term "model" to refer to these classes and instances that interact with the database.
But Pydantic also uses the term "model" to refer to something different, the data validation, conversion, and documentation classes and instances.
"""

from sqlalchemy import Table, Boolean, Column, MetaData, DateTime, String, JSON, Float, ForeignKey, Text, Integer
from sqlalchemy.sql.sqltypes import Integer, VARCHAR, ARRAY, TIMESTAMP
from db.database import Base
from sqlalchemy.sql import func


metadata = MetaData()

users = Table(
    "users",
    metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    <PERSON>umn("username", VARCHAR(50), unique=True),
    <PERSON><PERSON><PERSON>("display_name", VARCHAR(100), unique=False),
    <PERSON><PERSON><PERSON>("role", VARCHAR(20), default="user"),
    <PERSON>umn("status", VARCHAR(10), default="active"),
    <PERSON>umn("created_at", TIMESTAMP, server_default=func.now()),
    <PERSON><PERSON><PERSON>("updated_at", TIMESTAMP, onupdate=func.now()),
    <PERSON><PERSON><PERSON>("given_name", VARCHAR(50), unique=False),
    <PERSON><PERSON><PERSON>("surname", VARCHAR(50), unique=False),
    Column("department_code", VARCHAR(20), unique=False),
    Column("department", VARCHAR(50), unique=False),
    Column("job_title", VARCHAR(50), unique=False),
    Column("manager", VARCHAR(50), unique=False),
    schema="dataquest",
)

notifications = Table(
    "notifications",
    metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    Column("user_id", Integer, ForeignKey("users.id"), nullable=False),  # Giả sử bạn có bảng users
    Column("title", String(255), nullable=True),
    Column("content", Text, nullable=False),
    Column("type", String(50), nullable=True),  # e.g., "approval_request", "system_update"
    Column("is_read", Boolean, default=False),
    Column("created_at", DateTime(timezone=True), server_default=func.now()),
    schema="dataquest",
)

users_wlp = Table(
    "users",
    metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    Column("username", VARCHAR(20), unique=True),
    Column("is_admin", Boolean, default=False),
    Column("status", VARCHAR(10), default="active"),
    Column("role", ARRAY(VARCHAR(20)), default=[""]),
    Column("role_lvl1", ARRAY(VARCHAR(20)), default=[""]),
    # Column("role_lvl2", ARRAY(VARCHAR(20)), default=[""]),
    # Column("role_lvl3", ARRAY(VARCHAR(20)), default=[""]),
    Column("department", VARCHAR(20), default=""),
    Column("created_at", TIMESTAMP, server_default=func.now()),
    Column("updated_at", TIMESTAMP, onupdate=func.now()),
    schema="whitelist",
)

users_ads = Table(
    "users_new",
    metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    Column("username", String(50), unique=True, nullable=False),
    Column("is_admin", Boolean, default=False),
    Column("status", String(10), default="active"),
    Column("role", ARRAY(String), default=[]),
    Column("hashed_password", String(100)),
    Column("user_group", String(30)),
    Column("user_type", String(20)),
    Column("created_at", TIMESTAMP, server_default=func.now()),
    Column("updated_at", TIMESTAMP, onupdate=func.now()),
    schema="ads"
)

users_sbv = Table(
    "users",
    metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    Column("username", String(50), unique=True, nullable=False),
    Column("is_admin", Boolean, default=True),
    Column("status", String(10), default="active"),
    Column("role", String, default="all"),
    Column("hashed_password", String(100)),
    Column("created_at", TIMESTAMP, server_default=func.now()),
    Column("updated_at", TIMESTAMP, onupdate=func.now()),
    schema="sbv"
)

users_ecommerce = Table(
    "users",
    metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    Column("username", String(50), unique=True, nullable=False),
    Column("is_admin", Boolean, default=False),
    Column("status", String(10), default="active"),
    Column("role", String),
    Column("hashed_password", String(100)),
    Column("created_at", TIMESTAMP, server_default=func.now()),
    Column("updated_at", TIMESTAMP, onupdate=func.now()),
    schema="ecommerce"
)

requests = Table(
    "requests",
    metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    Column("title", String, nullable=False),
    Column("description", Text),
    Column("request_type", String(30), nullable=False),
    Column("data_needed", Text),
    Column("platform_name", String(50)),
    Column("db_name", String(50)),
    Column("report_needed", Text), #json
    Column("due_date", DateTime),
    Column("status", String(30), nullable=False, default="pending"),
    Column("requester_id", Integer, ForeignKey("dataquest.users.id", ondelete="SET NULL")),
    Column("reviewer_id", Integer, ForeignKey("dataquest.users.id", ondelete="SET NULL")),
    Column("approver_id", Integer, ForeignKey("dataquest.users.id", ondelete="SET NULL")),
    Column("executor_id", Integer, ForeignKey("dataquest.users.id", ondelete="SET NULL")),
    Column("executors", ARRAY(String(50)), nullable=False, default=[]), # list username
    Column("created_at", TIMESTAMP, server_default=func.now()),
    Column("updated_at", TIMESTAMP, onupdate=func.now()),
    schema="dataquest"
)

request_comments = Table(
    "request_comments",
    metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    Column("request_id", Integer, ForeignKey("dataquest.requests.id", ondelete="CASCADE")),
    Column("user_id", Integer, ForeignKey("dataquest.users.id", ondelete="SET NULL")),
    Column("comment", Text, nullable=False),
    Column("created_at", TIMESTAMP, server_default=func.now()),
    schema="dataquest"
)

request_logs = Table(
    "request_logs",
    metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    Column("request_id", Integer, ForeignKey("dataquest.requests.id", ondelete="CASCADE")),
    Column("log_type", String),
    Column("value", String),
    Column("changed_by", Integer, ForeignKey("dataquest.users.id")),
    Column("created_at", TIMESTAMP, server_default=func.now()),
    Column("note", Text),
    schema="dataquest"
)

approval_config = Table(
    "approval_config",
    metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    Column("request_type", String),
    Column("platform", String),
    Column("role", String),
    Column("user_id", Integer, ForeignKey("dataquest.users.id")),
    Column("created_at", TIMESTAMP, server_default=func.now()),
    schema="dataquest"
)

release_notes = Table(
    "release_notes",
    metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    Column("version", VARCHAR(20)),
    Column("status", VARCHAR(20)),
    Column("complete_percent", Integer),  # Using Integer for smallint
    Column("release_date", DateTime),
    Column("content", Text),
    schema="dataquest"
)

atlas_workbooks = Table(
    "atlas_workbooks",
    metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    Column("name", VARCHAR(100)),  # title
    Column("description", VARCHAR(200)),
    Column("thumbnail_id", VARCHAR(100)),  # defaultViewThumbnailId
    Column("owner", VARCHAR(20)),  # ownerAlias
    Column("owner_name", VARCHAR(50)),  # ownerName
    Column("owner_email", VARCHAR(50)),  # ownerEmail
    Column("created_at", TIMESTAMP, server_default=func.now()),  # createdTime
    Column("updated_at", TIMESTAMP, onupdate=func.now()),
    schema="dataquest"
)

atlas_views = Table(
    "atlas_views",
    metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    Column("name", VARCHAR(100)),  # title
    Column("path", VARCHAR(200)),
    Column("thumbnail_id", VARCHAR(100)),  # thumbnailId
    Column("workbook_id", Integer, ForeignKey("dataquest.atlas_workbooks.id")),
    Column("owner", VARCHAR(20)),  # ownerAlias
    Column("owner_name", VARCHAR(50)),  # ownerName
    Column("owner_email", VARCHAR(50)),  # ownerEmail
    Column("created_at", TIMESTAMP, server_default=func.now()),  # createdTime
    Column("updated_at", TIMESTAMP, onupdate=func.now()),
    schema="dataquest"
)

feedback = Table(
    "feedback",
    metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    Column("subject", VARCHAR(255), nullable=False),
    Column("category", VARCHAR(100), nullable=False),
    Column("description", Text, nullable=False),
    Column("priority", VARCHAR(50), nullable=False),
    Column("attachments", JSON, nullable=True),  # Store attachment info as JSON
    Column("user_id", Integer, ForeignKey("dataquest.users.id", ondelete="SET NULL")),
    Column("status", VARCHAR(50), default="open"),
    Column("created_at", TIMESTAMP, server_default=func.now()),
    Column("updated_at", TIMESTAMP, onupdate=func.now()),
    schema="dataquest"
)

files = Table(
    "files",
    metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    Column("filename", VARCHAR(255), nullable=False),
    Column("original_filename", VARCHAR(255), nullable=False),
    Column("file_path", VARCHAR(500), nullable=False),
    Column("file_size", Integer, nullable=False),
    Column("mime_type", VARCHAR(100), nullable=True),
    Column("description", Text, nullable=True),
    Column("user_id", Integer, ForeignKey("dataquest.users.id", ondelete="SET NULL")),
    Column("status", VARCHAR(50), default="active"),
    Column("created_at", TIMESTAMP, server_default=func.now()),
    Column("updated_at", TIMESTAMP, onupdate=func.now()),
    schema="dataquest"
)

